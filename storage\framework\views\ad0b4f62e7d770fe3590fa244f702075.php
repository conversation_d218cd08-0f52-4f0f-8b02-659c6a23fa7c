<?php $__env->startSection('content'); ?>
    
        
            
                
                    

                    
                        
                        
                            
                            
                        

                        
                            

                            
                                

                                
                                    

                                    
                                    
                                        
                                        
                                    
                                    
                                

                            
                                
                                    
                                        
                                        
                                    
                                
                            
                        
                    
                
            
        


    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <!--begin::Aside-->
        <div class="d-flex flex-center w-50 pt-15 pt-lg-0 px-10">
            <!--begin::Aside-->
            <div class="d-flex flex-center flex-lg-start flex-column w-100 align-items-center">

                <!--begin::Wrapper-->
                <div class=" d-flex flex-center flex-column w-75 px-lg-10 pb-15  ">
                    <form class="form w-100" novalidate="novalidate" method="POST" action="<?php echo e(route('password.email')); ?>">
                        <?php echo csrf_field(); ?>
                        <!--begin::Heading-->
                        <div class="text-center mb-11">
                            <!--begin::Title-->
                            <h1 class="text-dark fw-bolder mb-3">Reset Password</h1>
                            <!--end::Title-->

                        </div>
                        <!--begin::Heading-->


                        <!--begin::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Email-->
                            <input id="email" type="email" placeholder="Email"
                                class="form-control bg-transparent <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email"
                                value="<?php echo e(old('email')); ?>" required autocomplete="email">

                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="invalid-feedback" role="alert">
                                    <strong><?php echo e($message); ?></strong>
                                </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <!--end::Email-->
                        </div>
                        <!--end::Input group=-->
                        <!--begin::Submit button-->
                        <div class="d-grid ">
                            <button type="submit" id="kt_password_reset_submit" class="blue-btn">
                                <!--begin::Indicator label-->
                                <span class="indicator-label">Send</span>
                                <!--end::Indicator label-->
                                <!--begin::Indicator progress-->
                                <span class="indicator-progress">Please wait...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                <!--end::Indicator progress-->
                            </button>
                        </div>
                        <!--end::Submit button-->
                    </form>
                    <!--end::Form-->

                    <div class="site_logo pt-15">
                        <a href="<?php echo e(url('/')); ?>" class="text-center">
                            <img src="<?php echo e(asset('website') . '/' . setting()->logo); ?>" alt="icon">
                            <h4 class="blue-text pt-2"> Stylenest </h4>
                        </a>
                    </div>
                </div>
                <!--end::Wrapper-->

            </div>
            <!--begin::Aside-->
        </div>


        <div class="w-50 login-side-image">
            <img src="<?php echo e(asset('website')); ?>/assets/images/login-banner.png" alt="icon">
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function () {
            // Check for session status message and show SweetAlert
            <?php if(session('status')): ?>
                Swal.fire({
                    title: "Email Sent!",
                    text: "Password reset link has been sent to your email address. Please check your inbox and follow the instructions.",
                    icon: "success",
                    confirmButtonText: "OK"
                });
            <?php endif; ?>

            // Check for validation errors and show SweetAlert
            <?php if($errors->has('email')): ?>
                Swal.fire({
                    title: "Error",
                    text: "<?php echo e($errors->first('email')); ?>",
                    icon: "error"
                });
            <?php endif; ?>
                // submitBtn.find('.indicator-progress').hide();
            });
    </script>


    <script>
        $('#email').on('input', function () {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        });

    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/auth/passwords/email.blade.php ENDPATH**/ ?>