@extends(auth()->check() ? (auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master') : 'website.layout.master')

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard bg-color addfamily padding-block">
        <div id="kt_app_content_container" class="container py-5 px-5">
            <div class="row row-gap-5">

              @if (auth()->check() && auth()->user()->hasRole(['admin', 'super admin']))

                <div class="col-md-2 mb-10"> 
                    <a href="{{ url()->previous() }}" class="add-btn mb-8 w-50"> <i class="fas fa-long-arrow-alt-left pe-3"></i> Back </a> 
                </div>

                @endif

                <div class="col-md-12 d-flex gap-8 align-items-center ms-0 px-0">
                    <div class="pro-stylist-logo">
                        <img src="{{ asset('website') . '/' . $user->profile->pic ?? '' }}">
                    </div>

                    <div class="w-100">
                        <h5>{{ $user->name ?? '' }}
                            @if ($user->is_featured)
                                <span class="top-rated ms-5">
                                    <img src="{{ asset('website') }}/assets/images/top-rated-star.png" class="img-fluid">
                                    FEATURED
                                </span>
                            @endif
                        </h5>
                        <div class="d-flex align-items-center">
                            <p class="fs-13 semi-bold dark-blue"><span><i
                                        class="fas fa-star pe-3"></i></span>{{ $user->averageRating > 0 ? $user->averageRating : 'No rating' }}
                                <span>({{ $user->totalReviews }})</span>
                            </p>
                            @php
                                $opening = $user->allOpeningHours->where('type', 'open')->first();
                                $dateTime = new DateTime($opening?->close);
                                $formattedTime = $dateTime->format('H:i');
                            @endphp
                            <ul class="d-flex gap-12 sub-details">
                                <li class="fs-13">Open until {{ $formattedTime ?? '' }}</li>
                                <li class="fs-13">{{ $user->profile->location ?? '' }}
                                </li>
                                <li class="fs-13"><span class="fs-14 deep-blue cursor-pointer"
                                        onclick="getDirections({{ $user->profile->lat ?? '' }}, {{ $user->profile->lng ?? '' }})">
                                        <i class="fas fa-external-link-alt fa-4"> </i> Get Directions </span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="d-flex flex-end ms-5 align-items-center heart-fav">
                        <button style="all: unset" id="generateShortUrlBtn" data-user-id="{{ $user->ids }}">
                            <img src="{{ asset('website') }}/assets/images/upload.svg">
                        </button>
                        @if (auth()->check() && auth()->user()->hasRole('customer'))
                            <button class="favorite-icon main-heading-icon" style="all: unset" data-user-id="{{ $user->id }}">
                                <i class="fa-heart fa-5 {{ $isFavorited ? 'fas text-danger' : 'far' }}" id="heart-icon"></i>
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        @if ($user->galleries->count() > 0)
            <div class="container-fluid professional-swiper banner-swiper">
                <div class="row">
                    <div class="col-md-12">
                        <div class="swiper mySwiper gallerySwiper">
                            <div class="swiper-wrapper">
                                @foreach ($user->galleries as $gallery)
                                    <div class="swiper-slide"><img src="{{ asset('website') . '/' . $gallery->image }}">
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-pagination"></div>

                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="container">
            <div class="row">
                @if ($user->product_cerficates->count() > 0)
                    <div class="col-md-12 mb-10">
                        <div class="swiper product-certifications certifications-logo pt-1">
                            <p class="fs-16 Sora bold">Product Certifications</p>
                            <div class="swiper-wrapper">
                                @foreach ($user->product_cerficates as $productCertification)
                                    <div class="swiper-slide">
                                        <img src="{{ asset('website') . '/' . $productCertification->image }}">
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-button-next product-certifications-next"></div>
                            <div class="swiper-button-prev product-certifications-prev"></div>
                        </div>
                    </div>
                @endif

                @if ($user->introCards->count() > 0)
                    <div class="col-md-12 mb-10">
                        <div class="row gy-4">
                            @foreach ($user->introCards as $card)
                                <div class="col-md-4 ">
                                    <div class="guarantee-section h-100">
                                        <div class="d-flex gap-5 cards h-100">
                                            <img class="mt-2" src="{{ asset('website') . '/' . $card->image }}" width="50px" height="50px">
                                            <div>
                                                <p class="fs-16 bold mb-0 card-heading">{{ $card->heading ?? '' }} </p>
                                                <p class="fs-16 bold light-gray mb-0 card-description">{{ $card->description ?? '' }}</p>
                                               

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="col-md-8">
                    <div class="container mb-5 profes-profile-services">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- Search and Title Section -->
                                <div class="d-flex justify-content-between mb-10">
                                    <div class="d-flex align-items-center">
                                        <h4 class="fs-24 sora black me-3">Services</h4>
                                        <small id="search-results-count" class="text-muted" style="display: none;"></small>
                                    </div>
                                    <div class="search-bar d-flex align-items-center position-relative">
                                        <i class="fa-solid fa-magnifying-glass me-3 search-icon"></i>
                                        <input type="text" id="service-search" placeholder="Search services..."
                                            autocomplete="off">
                                        <i class="fa-solid fa-times search-clear-icon" id="search-clear"
                                            style="display: none;"></i>
                                    </div>
                                </div>

                                <!-- Categories Section with Filter Button in Same Line -->
                                <div class="d-flex justify-content-between align-items-center pro-service-tabs">
                                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                                        <!-- "All" Tab (Always Active) -->
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="all-services-tab" data-bs-toggle="pill"
                                                data-bs-target="#all-services" type="button" role="tab"
                                                aria-controls="all-services" aria-selected="true">
                                                All
                                            </button>
                                        </li>
                                        <!-- Category Tabs -->
                                        @foreach ($userCategories as $index => $category)
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link category-tab-btn" id="category-{{ $category->id }}-tab"
                                                    data-bs-toggle="pill" data-bs-target="#category-{{ $category->id }}"
                                                    type="button" role="tab" data-category-id="{{ $category->id }}"
                                                    aria-controls="category-{{ $category->id }}" aria-selected="false">
                                                    {{ $category->name }}
                                                </button>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>

                                <!-- Tab Content Section -->
                                <div class="tab-content" id="pills-tabContent">
                                    <!-- All Services Tab (Always Visible) -->
                                    <div class="tab-pane fade show active" id="all-services" role="tabpanel"
                                        aria-labelledby="all-services-tab">
                                        <div class="row row-gap-5" id="services-list">
                                            <table id="responsiveTable" class=" display wallet-history-table"
                                                style="width: 100%">
                                                <thead>
                                                    <tr>
                                                        <th></th>
                                                        <th>Duration</th>
                                                        <th>Price</th>
                                                        <th>Category</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @forelse ($user->services->where('status', 1) as $service)
                                                        <tr class="service-item" data-price="{{ $service->price }}"
                                                            data-duration="{{ $service->duration }}"
                                                            data-service-name="{{ strtolower($service->name) }}"
                                                            data-service-description="{{ strtolower($service->description) }}"
                                                            data-category-name="{{ strtolower($service->category->name ?? '') }}"
                                                            data-subcategory-name="{{ strtolower($service->subcategory->name ?? '') }}">
                                                            <td>
                                                                <div class="card flex-row shadow-none p-0 gap-3">
                                                                    <div class="card-header p-0 border-0 align-items-start">
                                                                        <img src="{{ asset('website') . '/' . $service->image }}"
                                                                            alt="card-image"
                                                                            onerror="this.src='{{ asset('website/assets/images/default.png') }}'" />
                                                                    </div>
                                                                    <div
                                                                        class="card-body p-0 d-flex flex-column justify-content-center">
                                                                        <p class="fs-16 regular black service-name mb-0">
                                                                            {{ $service->name }}
                                                                        </p>
                                                                        <p
                                                                            class="light-black opacity-6 fs-14 normal service-description mb-0">
                                                                            {{ $service->description }}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>{{ $service->duration }} minutes</td>
                                                            <td>${{ $service->price }}</td>
                                                            <td class="service-category">
                                                                {{ $service->category->name ?? 'N/A' }}
                                                            </td>
                                                            <td data-label="" class="text-end">
                                                                @auth
                                                                    @if (auth()->user()->hasRole('customer'))
                                                                        <button
                                                                            class="add-btn rounded-1 fs-16 add-to-cart-btn w-md-120px"
                                                                            data-bs-toggle="modal" data-id="{{ $service->ids }}"
                                                                            data-bs-target="#service-details">
                                                                            Book Now
                                                                        </button>
                                                                    @endif
                                                                @else
                                                                    <button onclick="window.location.href='{{ route('register') }}'"
                                                                        class="add-btn rounded-1 fs-16 w-md-180px w-sm-150px">
                                                                        Login to book
                                                                    </button>
                                                                @endauth
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="4" class="text-center py-4">
                                                                <p class="fs-16 text-muted">No services available</p>
                                                            </td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Category Tabs Content -->
                                    @foreach ($userCategories as $index => $category)
                                        <div class="tab-pane fade" id="category-{{ $category->id }}" role="tabpanel"
                                            aria-labelledby="category-{{ $category->id }}-tab">
                                            <div class="d-flex justify-content-between flex-start flex-wrap">
                                                <!-- Subcategories for the specific category -->
                                                <ul class="nav nav-pills mb-10" class="pro-subcategories-tabs"
                                                    id="subcategory-pills-tab" role="tablist">
                                                    @if (isset($userSubcategoriesByCategory[$category->id]))
                                                        @foreach ($userSubcategoriesByCategory[$category->id] as $subcategory)
                                                            <li class="nav-item" role="presentation">
                                                                <button class="nav-link {{ $loop->first ? 'active' : '' }}"
                                                                    id="subcategory-{{ $subcategory->id }}-tab" data-bs-toggle="pill"
                                                                    data-bs-target="#subcategory-{{ $subcategory->id }}" type="button"
                                                                    role="tab" aria-controls="subcategory-{{ $subcategory->id }}"
                                                                    aria-selected="{{ $loop->first ? 'true' : 'false' }}">
                                                                    {{ $subcategory->name }}
                                                                </button>
                                                            </li>
                                                        @endforeach
                                                    @else
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" type="button">
                                                                No subcategories available
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>

                                            <!-- Services under each subcategory -->
                                            <div class="tab-content" id="subcategory-pills-tabContent">
                                                @if (isset($userSubcategoriesByCategory[$category->id]))
                                                    @foreach ($userSubcategoriesByCategory[$category->id] as $subcategory)
                                                        <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}"
                                                            id="subcategory-{{ $subcategory->id }}" role="tabpanel"
                                                            aria-labelledby="subcategory-{{ $subcategory->id }}-tab">
                                                            <div class="row row-gap-5">
                                                                <table id="responsiveTable" class="display wallet-history-table"
                                                                    style="width: 100%">
                                                                    <thead>
                                                                        <tr>
                                                                            <th></th>
                                                                            <th>Duration</th>
                                                                            <th>Price</th>
                                                                            <th>Category</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @php
                                                                            // Get only this professional's services that belong to this subcategory
                                                                            $professionalServicesInSubcategory = $user->services->where(
                                                                                'subcategory_id',
                                                                                $subcategory->id,
                                                                            )->where('status', 1);
                                                                        @endphp
                                                                        @forelse ($professionalServicesInSubcategory as $service)
                                                                            <tr class="service-item" data-price="{{ $service->price }}"
                                                                                data-duration="{{ $service->duration }}"
                                                                                data-service-name="{{ strtolower($service->name) }}"
                                                                                data-service-description="{{ strtolower($service->description) }}"
                                                                                data-category-name="{{ strtolower($service->category->name ?? '') }}"
                                                                                data-subcategory-name="{{ strtolower($service->subcategory->name ?? '') }}">
                                                                                <td>
                                                                                    <div class="card flex-row shadow-none p-0 gap-3">
                                                                                        <div
                                                                                            class="card-header p-0 border-0 align-items-start">
                                                                                            <img src="{{ asset('website') . '/' . $service->image }}"
                                                                                                alt="card-image" />
                                                                                        </div>
                                                                                        <div
                                                                                            class="card-body p-0 d-flex flex-column justify-content-center">
                                                                                            <p
                                                                                                class="fs-16 regular black service-name mb-0">
                                                                                                {{ $service->name }}
                                                                                            </p>
                                                                                            <p
                                                                                                class="light-black opacity-6 fs-14 normal mb-0 service-description">
                                                                                                {{ $service->description }}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                <td>{{ $service->duration }} minutes</td>
                                                                                <td>${{ $service->price }}</td>
                                                                                <td class="service-category">
                                                                                    {{ $service->category->name }}
                                                                                </td>
                                                                                <td data-label="" class="text-end">
                                                                                    @auth
                                                                                        @if (auth()->user()->hasRole('customer'))
                                                                                            <button
                                                                                                class="add-btn rounded-1 fs-16 add-to-cart-btn w-md-120px"
                                                                                                data-bs-toggle="modal" data-id="{{ $service->ids }}"
                                                                                                data-bs-target="#service-details">
                                                                                                Book Now
                                                                                            </button>
                                                                                        @endif
                                                                                    @else
                                                                                        <button onclick="window.location.href='{{ route('register') }}'"
                                                                                            class="add-btn rounded-1 fs-16 w-md-180px w-sm-150px">
                                                                                            Login to Book
                                                                                        </button>
                                                                                    @endauth
                                                                                </td>
                                                                            </tr>
                                                                        @empty
                                                                            <tr>
                                                                                <td colspan="5" class="text-center py-4">
                                                                                    <p class="fs-16 text-muted">No services available</p>
                                                                                </td>
                                                                            </tr>
                                                                        @endforelse
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="tab-pane fade show active" role="tabpanel">
                                                        <div class="row row-gap-5">
                                                            <div class="col-12 text-center py-4">
                                                                <p class="fs-16 text-muted">No subcategories or services
                                                                    available for this category</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            @if ($reviews->count() > 0)
                                <div class="col-md-12 mb-15">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5>Reviews</h5>
                                            <p class="black"> <i class="fas fa-star black"></i>
                                                {{ $user->averageRating > 0 ? $user->averageRating : 'No rating' }}
                                                ({{ $reviews->count() }})</p>
                                        </div>
                                        {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                    </div>
                                    <div class="swiper mySwiper review-swiper">
                                        <div class="swiper-wrapper">
                                            @foreach ($reviews as $review)
                                                <div class="swiper-slide">
                                                    <div class="guarantee-section rounded-4 p-10">
                                                        <div class="d-flex" style="gap: 10px;">
                                                            <img src="{{ asset('website') . '/' . $review->user->profile->pic }}"
                                                                alt="Reviewer Photo"
                                                                style="width: 48px; height: 48px; border-radius: 50%;"
                                                                onerror="this.src='{{ asset('website/assets/images/default.png') }}'">
                                                            <div>
                                                                <p class="fs-15 Sora bold mb-0">{{ $review->user->name }}
                                                                </p>
                                                                <p class="dark-cool-gray fs-14">
                                                                    {{ $review->created_at->format('d M Y') }} at
                                                                    {{ $review->created_at->format('h:i A') }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <p class="fs-14">
                                                                {{ $review->comment }}
                                                            </p>
                                                            <div>
                                                                <span> <i class="fas fa-star"></i> <i class="fas fa-star"></i>
                                                                    <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i
                                                                        class="fas fa-star"></i></span> <span
                                                                    class="fs-14 bold">{{ $review->average_rating }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                            <!-- Add more .swiper-slide items here for additional reviews -->
                                        </div>
                                        <!-- Navigation buttons -->
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            @endif

                            @if ($user->staffs->where('status', 1)->count() > 0)
                                <div class="col-md-12 mb-15 position-relative">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5>Meet The Team</h5>
                                        {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                    </div>

                                    <div class="swiper meet-the-team-swiper position-unset">
                                        <div class="swiper-wrapper">
                                            @foreach ($user->staffs->where('status', 1) as $staff)
                                                <div class="swiper-slide h-auto">
                                                    <div class="guarantee-section p-5 h-100">
                                                        <div class="d-flex flex-column justify-content-center align-items-center"
                                                            style="gap: 10px;">
                                                            <img src="{{ asset('website') . '/' . $staff->image }}"
                                                                alt="Reviewer Photo">
                                                            <p class="fs-16 bold mb-2">{{ $staff->name ?? '' }}</p>

                                                            <ul class="pro-social-icons">
                                                                @if ($staff->facebook)
                                                                    <a href="{{ $staff->facebook ?? '' }}">
                                                                        <li> <img
                                                                                src="http://anders.democustomprojects.com/website/assets/images/facebook-original.svg"
                                                                                class="img-fluid " alt="card-image">
                                                                        </li>
                                                                    </a>
                                                                @endif
                                                                @if ($staff->instagram)
                                                                    <a href="{{ $staff->instagram ?? '' }}">
                                                                        <li> <img
                                                                                src="http://anders.democustomprojects.com/website/assets/images/instagram-original.svg"
                                                                                class="img-fluid " alt="card-image">
                                                                        </li>
                                                                    </a>
                                                                @endif
                                                                @if ($staff->youtube)
                                                                    <a href="{{ $staff->youtube ?? '' }}">
                                                                        <li> <img
                                                                                src="http://anders.democustomprojects.com/website/assets/images/twitter-original.svg"
                                                                                class="img-fluid " alt="card-image">
                                                                        </li>
                                                                    </a>
                                                                @endif
                                                                @if ($staff->tiktok)
                                                                    <a href="{{ $staff->tiktok ?? '' }}">
                                                                        <li> <img
                                                                                src="http://anders.democustomprojects.com/website/assets/images/tiktok-original.svg"
                                                                                class="img-fluid " alt="card-image">
                                                                        </li>
                                                                    </a>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                            <!-- Add more .swiper-slide items here for additional reviews -->
                                        </div>

                                        <!-- Navigation buttons -->
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>


                                </div>
                            @endif

                            @if($user->portfolios->count() > 0)
                                <div class="col-md-12 mb-15">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5>Portfolio</h5>
                                        @if($user->portfolios->count() > 6)
                                            <a href="#!" class="black-border-btn" id="seeAllBtn"> See All</a>
                                        @endif
                                    </div>

                                    <div class="professional-image-gallery gallery" id="gallery">
                                        @php
                                            $portfolioCount = $user->portfolios->count();
                                            $showExpandButton = $portfolioCount > 6;
                                            $visiblePortfolios = $showExpandButton ? $user->portfolios->take(5) : $user->portfolios;
                                            $remainingCount = $portfolioCount - 5;
                                        @endphp

                                        @foreach ($visiblePortfolios as $portfolio)
                                            <div class="image-card"><img src="{{ asset('website') . '/' . $portfolio->image }}"
                                                    alt="portfolio"></div>
                                        @endforeach

                                        @if($showExpandButton)
                                            <div class="image-card expand-portfolio-btn"
                                                style="position: relative; cursor: pointer;">
                                                <img src="{{ asset('website') . '/' . $user->portfolios->skip(5)->first()->image }}"
                                                    alt="portfolio" style="filter: brightness(0.4);">
                                                <div
                                                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 24px; font-weight: bold; text-align: center;">
                                                    +{{ $remainingCount }}
                                                </div>
                                            </div>
                                        @endif

                                        <!-- Hidden portfolio items for expansion -->
                                        @if($showExpandButton)
                                            @foreach ($user->portfolios->skip(5) as $portfolio)
                                                <div class="image-card hidden-portfolio" style="display: none;"><img
                                                        src="{{ asset('website') . '/' . $portfolio->image }}" alt="portfolio"></div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            @endif

                            @if ($user->certificates->where('status', 1)->count() > 0)
                                <div class="col-md-12 mb-10 position-relative">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5 class="fs-24">Certifications & Licenses</h5>
                                        {{-- <a href="#!" class="black-border-btn"> See All</a> --}}
                                    </div>

                                    <div class="swiper cert-swiper">
                                        <div class="swiper-wrapper">
                                            @foreach ($user->certificates->where('status', 1) as $certificate)
                                                <div class="swiper-slide h-auto">
                                                    <div class="guarantee-section p-5 h-100">
                                                        <div class=" certificate-license-swiper d-flex justify-content-center flex-column">
                                                            <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                alt="Reviewer Photo"
                                                                onerror="this.src='{{ asset('website/assets/images/cert-placeholder.svg') }}'">

                                                            <p class="fs-14 bold mb-2"> {{ $certificate->name ?? '' }}
                                                            </p>
                                                            <p class="link-gray fs-14 mb-2">Issued by: <span class="black fs-14">
                                                                    {{ $certificate->issued_by ?? '' }}</span></p>
                                                            <p class="link-gray fs-14 mb-2">Issue Date: <span class="black fs-14">
                                                                    {{ $certificate->issued_date ?? '' }}</span></p>
                                                            <p class="link-gray fs-14"> Expiry Date:<span class="black fs-14">
                                                                    {{ $certificate->end_date ?? '' }}</span></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            @endif

                            @if ($user->profile && $user->profile->lat && $user->profile->lng)
                                <div class="col-md-12 mb-10" id="directions">
                                    <h5 class="fs-24 mb-8">Where to find us</h5>
                                    <!-- Professional Location Map -->
                                    <div id="professional-location-map" class="professional-map-container"
                                        style="width:100%; height: 500px; border-radius: 8px; border: 1px solid #e1e5e9;">
                                    </div>
                                    <p class="fs-14 light-black pt-5">
                                        {{ $user->profile->location ?? ($user->profile->city . ', ' . $user->profile->country ?? 'Location not specified') }}
                                        <span class="fs-14 ms-5 deep-blue cursor-pointer"
                                            onclick="getDirections({{ $user->profile->lat }}, {{ $user->profile->lng }})">
                                            <i class="fas fa-external-link-alt "> </i> Get Directions
                                        </span>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-4 time-container">
                    <div class="guarantee-section mb-10 px-5 pt-10">
                        @if (auth()->check() && auth()->user()->hasRole('customer'))
                            <a href="{{ route('chats.index', ['professional_id' => $user->ids]) }}"
                                class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        @else
                            <a href="#!" class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        @endif
                        <div class="hours-container mt-10">
                            <div class="hours-toggle open" onclick="
                                            document.querySelector('.hours-list').classList.toggle('active');
                                            this.classList.toggle('expanded');">
                                <i class="far fa-clock large-icon"></i>
                                <span class="fs-14 Sora"> <span class="green"> Opening </span> Hours</span>
                                <span class="chevron"> <i class="fas fa-angle-up"></i></span>
                            </div>
                            <ul class="hours-list ">
                                @foreach ($user->allOpeningHours as $openingHour)
                                    <li class="hours-row">
                                        <span class="day"><i class="dot">●</i> {{ $openingHour->day }}</span>
                                        @if ($openingHour->open && $openingHour?->close)
                                            <span
                                                class="time">{{ \Carbon\Carbon::parse($openingHour->open)->format('H:i') ?? 'Closed' }}
                                                –
                                                {{ \Carbon\Carbon::parse($openingHour->close)->format('H:i') ?? 'Closed' }}
                                            </span>
                                        @else
                                            <span class="time">Closed</span>
                                        @endif
                                    </li>
                                @endforeach
                            </ul>

                            <div class="pt-6">
                                <p class="fs-14 Sora light-black">
                                    <a href="tel:{{ $user->profile->phone ?? '' }}"><span>
                                            <img src="{{ asset('website') }}/assets/images/phone-call.svg"
                                                class="img-fluid " alt="card-image">
                                        </span> {{ $user->profile->phone ?? '' }}
                                    </a>
                                </p>
                                @if ($user->profile->city && $user->profile->country)
                                    <p class="fs-14 Sora light-black"> <span> <i class="bi bi-geo-alt large-icon"></i>
                                        </span>
                                        {{ $user->profile->city ?? '' }} , {{ $user->profile->country ?? '' }} </p>
                                @endif
                                <ul class="service-social-icons">
                                    @forelse ($user->socials as $social)
                                        <a href="{{ $social->link }}" target="_blank" class="logo-box">
                                            <li> <img src="{{ asset('website') . '/' . $social->socialPlatform->image }}"
                                                    class="img-fluid " alt="card-image"
                                                    onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'">
                                            </li>
                                        </a>
                                    @empty
                                        <p>No Socials Found</p>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('dashboard.templates.modal.add-service-details-modal')
        @include('dashboard.templates.modal.professional-services-filter')
        @include('dashboard.templates.modal.professional-service-category-modal')
@endsection

    @push('js')
        <script>
            function toggleHours(element) {
                document.querySelector('.hours-list').classList.toggle('active');
                element.classList.toggle('expanded');
            }
        </script>


        <script>
            function initSwiper(selector, options = {}) {
                return new Swiper(selector, Object.assign({
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    }
                }, options));
            }

            // Init sliders
            initSwiper(".review-swiper", {
                slidesPerView: 2,
                spaceBetween: 15,
            });

            initSwiper(".cert-swiper", {
                slidesPerView: 4,
                spaceBetween: 10,
            });

            initSwiper(".meet-the-team-swiper", {
                slidesPerView: 4,
                spaceBetween: 10,
                loopFillGroupWithBlank: false,
            });

            initSwiper(".gallerySwiper", {
                slidesPerView: 3,
                spaceBetween: 10,
                loopFillGroupWithBlank: false,
            });

            initSwiper(".mySwiper3", {
                slidesPerView: 5,
                spaceBetween: 20,
            });

            initSwiper(".product-certifications", {
                loop: true,
                slidesPerView: 15,
                spaceBetween: 20,
                slidesPerGroup: 1,
                navigation: {
                    nextEl: ".swiper-button-next.product-certifications-next",
                    prevEl: ".swiper-button-prev.product-certifications-prev",
                },
                pagination: {
                    el: ".product-certifications-pagination",
                    clickable: true,
                    dynamicBullets: true,
                },
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 10,
                        slidesPerGroup: 1
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 15,
                        slidesPerGroup: 1
                    },
                    1024: {
                        slidesPerView: 6,
                        spaceBetween: 20,
                        slidesPerGroup: 1
                    },
                    1200: {
                        slidesPerView: 15,
                        spaceBetween: 20,
                        slidesPerGroup: 1
                    }
                }
            });

            initSwiper(".mySwiper2", {
                slidesPerView: 14,
                spaceBetween: 20,
            });
        </script>



        <script>
            function showMoreImages() {
                const hidden = document.querySelectorAll('.hidden-image');
                hidden.forEach(el => el.style.display = 'block');
                document.querySelector('.overlay').parentElement.remove();
            }
        </script>

        <script>
            $(document).ready(function () {
                // Portfolio expand/collapse functionality
                function expandPortfolio() {
                    const $hiddenPortfolios = $('.hidden-portfolio');
                    const $expandBtn = $('.expand-portfolio-btn');
                    const $seeAllBtn = $('#seeAllBtn');
                    const $gallery = $('#gallery');

                    // Show all hidden images
                    $hiddenPortfolios.show();
                    $expandBtn.hide();

                    // Change "See All" button to "See Less"
                    $seeAllBtn.text(' See Less');

                    // Add a "Show Less" button at the end if it doesn't exist
                    if ($('.collapse-portfolio-btn').length === 0) {
                        $gallery.append(`
                                        <div class="image-card collapse-portfolio-btn" style="position: relative; cursor: pointer; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; min-height: 150px;">
                                            <div style="text-align: center; color: #6c757d;">
                                                <i class="fas fa-chevron-up" style="font-size: 24px; margin-bottom: 8px;"></i>
                                                <div style="font-size: 14px; font-weight: 500;">Show Less</div>
                                            </div>
                                        </div>
                                    `);
                    }
                }

                function collapsePortfolio() {
                    const $hiddenPortfolios = $('.hidden-portfolio');
                    const $expandBtn = $('.expand-portfolio-btn');
                    const $seeAllBtn = $('#seeAllBtn');

                    // Hide additional images and show expand button
                    $hiddenPortfolios.hide();
                    $expandBtn.show();

                    // Change "See Less" button back to "See All"
                    $seeAllBtn.text(' See All');

                    // Remove collapse button
                    $('.collapse-portfolio-btn').remove();
                }

                // Handle "+X more" button click
                $('.expand-portfolio-btn').on('click', function () {
                    const $hiddenPortfolios = $('.hidden-portfolio');

                    if ($hiddenPortfolios.is(':visible')) {
                        collapsePortfolio();
                    } else {
                        expandPortfolio();
                    }
                });

                // Handle "See All" button click
                $('#seeAllBtn').on('click', function (e) {
                    e.preventDefault();
                    const $hiddenPortfolios = $('.hidden-portfolio');

                    if ($hiddenPortfolios.is(':visible')) {
                        collapsePortfolio();
                    } else {
                        expandPortfolio();
                    }
                });

                // Handle collapse button click (the "Show Less" card at the end)
                $(document).on('click', '.collapse-portfolio-btn', function () {
                    collapsePortfolio();
                });
            });
        </script>

        <script>
            function getDirections(lat, lng) {
                if (lat && lng) {
                    // Open Google Maps with directions
                    const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=driving`;
                    window.open(directionsUrl, '_blank');
                } else {
                    alert('Location coordinates not available for this professional.');
                }
            }
        </script>
    @endpush

    @push('js')
        <script>
            $(document).ready(function () {
                // Handle category tab clicks
                $('.category-tab-btn').on('click', function () {
                    var categoryId = $(this).data('category-id');

                    // Remove active class from all category tabs (including All tab)
                    $('#all-services-tab, .category-tab-btn').removeClass('active').attr('aria-selected',
                        'false');
                    // Add active class to clicked tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');

                    // Show the selected category tab pane
                    $('#category-' + categoryId).addClass('show active');

                    // Activate the first subcategory tab within this category
                    var firstSubcategoryTab = $('#category-' + categoryId + ' .nav-pills .nav-link').first();
                    var firstSubcategoryPane = $('#category-' + categoryId + ' .tab-content .tab-pane').first();

                    // Remove active from all subcategory tabs in this category
                    $('#category-' + categoryId + ' .nav-pills .nav-link').removeClass('active').attr(
                        'aria-selected', 'false');
                    $('#category-' + categoryId + ' .tab-content .tab-pane').removeClass('show active');

                    // Activate first subcategory if it exists
                    if (firstSubcategoryTab.length > 0) {
                        firstSubcategoryTab.addClass('active').attr('aria-selected', 'true');
                        firstSubcategoryPane.addClass('show active');
                    }
                });

                // Handle All tab click
                $('#all-services-tab').on('click', function () {
                    // Remove active class from all category tabs
                    $('.category-tab-btn').removeClass('active').attr('aria-selected', 'false');
                    // Add active class to All tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');
                    // Show All services tab pane
                    $('#all-services').addClass('show active');
                });

                // Handle subcategory tab clicks - let Bootstrap handle the pill behavior
                $(document).on('click', '[id^="subcategory-"][id$="-tab"]', function (e) {
                    var subcategoryId = $(this).attr('id').replace('subcategory-', '').replace('-tab', '');
                    var categoryId = $(this).closest('.tab-pane').attr('id').replace('category-', '');
                    
                    // Update the search context to reflect the current subcategory
                    updateSearchContext('subcategory', categoryId, subcategoryId);
                });

                // Listen for Bootstrap pill events to ensure proper visual feedback
                $(document).on('shown.bs.tab', '[id^="subcategory-"][id$="-tab"]', function (e) {
                    var subcategoryId = $(e.target).attr('id').replace('subcategory-', '').replace('-tab', '');
                    var categoryId = $(e.target).closest('.tab-pane').attr('id').replace('category-', '');
                    
                    // Ensure the tab is visually active
                    $(e.target).addClass('active').attr('aria-selected', 'true');
                });

                // Dynamic Search Functionality
                $('#service-search').on('input', function () {
                    var searchTerm = $(this).val().toLowerCase().trim();

                    // Show/hide clear icon based on input
                    if (searchTerm.length > 0) {
                        $('#search-clear').show();
                    } else {
                        $('#search-clear').hide();
                    }

                    if (searchTerm === '') {
                        // Show all services when search is empty
                        showAllServices();
                        clearHighlights();
                        return;
                    }

                    // Store current active tab context
                    var currentActiveTab = getCurrentActiveTab();

                    // Hide all services first
                    hideAllServices();
                    clearHighlights();

                    // Search in all services across all tabs using data attributes
                    var hasResults = false;

                    // Search in all service items (both in "All Services" and category tabs)
                    $('tr.service-item').each(function () {
                        var $row = $(this);
                        var serviceName = $row.data('service-name') || '';
                        var serviceDescription = $row.data('service-description') || '';
                        var categoryName = $row.data('category-name') || '';
                        var subcategoryName = $row.data('subcategory-name') || '';

                        // Check if search term matches any of the service attributes
                        if (serviceName.includes(searchTerm) ||
                            serviceDescription.includes(searchTerm) ||
                            categoryName.includes(searchTerm) ||
                            subcategoryName.includes(searchTerm)) {
                            $row.show();
                            hasResults = true;
                        }
                    });

                    // Ensure subcategory tabs are visible if they have results
                    $('.tab-pane[id^="subcategory-"]').each(function () {
                        var $subcategoryPane = $(this);
                        var subcategoryId = $subcategoryPane.attr('id');
                        var hasSubcategoryResults = $subcategoryPane.find(
                            'tbody tr.service-item:visible').length > 0;

                        if (hasSubcategoryResults) {
                            $('#' + subcategoryId + '-tab').show();
                        } else {
                            $('#' + subcategoryId + '-tab').hide();
                        }
                    });

                    // Handle tab switching based on current context and where results are found
                    var allServicesResults = $('#all-services tbody tr.service-item:visible').length;
                    var categoryResults = $('.tab-pane[id^="category-"] tbody tr.service-item:visible').length;

                    // Check if current tab has results
                    var currentTabHasResults = false;
                    if (currentActiveTab.type === 'all') {
                        currentTabHasResults = allServicesResults > 0;
                    } else if (currentActiveTab.type === 'category') {
                        currentTabHasResults = $('#category-' + currentActiveTab.categoryId +
                            ' tbody tr.service-item:visible').length > 0;
                    } else if (currentActiveTab.type === 'subcategory') {
                        currentTabHasResults = $('#subcategory-' + currentActiveTab.subcategoryId +
                            ' tbody tr.service-item:visible').length > 0;
                    }

                    // If current tab has results, stay on it
                    if (currentTabHasResults) {
                        if (currentActiveTab.type === 'subcategory') {
                            // Make sure the subcategory tab is active and visible
                            $('#subcategory-' + currentActiveTab.subcategoryId + '-tab').addClass('active')
                                .attr('aria-selected', 'true');
                            $('#subcategory-' + currentActiveTab.subcategoryId).addClass('show active');

                            // Make sure parent category is also active
                            $('#category-' + currentActiveTab.categoryId + '-tab').addClass('active').attr(
                                'aria-selected', 'true');
                            $('#category-' + currentActiveTab.categoryId).addClass('show active');

                            // Deactivate other tabs
                            $('#all-services-tab').removeClass('active').attr('aria-selected', 'false');
                            $('#all-services').removeClass('show active');
                        } else if (currentActiveTab.type === 'category') {
                            // Stay on category tab
                            $('#category-' + currentActiveTab.categoryId + '-tab').addClass('active').attr(
                                'aria-selected', 'true');
                            $('#category-' + currentActiveTab.categoryId).addClass('show active');
                        }
                        // If type is 'all', it's already active
                    } else {
                        // Current tab has no results, find the best tab to switch to
                        if (allServicesResults > 0) {
                            $('#all-services-tab').click();
                        } else if (categoryResults > 0) {
                            // Find first category with results
                            $('.tab-pane[id^="category-"]').each(function () {
                                if ($(this).find('tbody tr.service-item:visible').length > 0) {
                                    var categoryId = $(this).attr('id').replace('category-', '');
                                    $('#category-' + categoryId + '-tab').click();
                                    return false; // Break the loop
                                }
                            });
                        }
                    }

                    // Show no results message if nothing found
                    if (!hasResults) {
                        showNoResultsMessage();
                        updateSearchCount(0);
                    } else {
                        hideNoResultsMessage();
                        var visibleCount = $('tr.service-item:visible').length;
                        updateSearchCount(visibleCount);
                    }
                });

                // Add search clear functionality
                $('#service-search').on('keyup', function (e) {
                    if (e.key === 'Escape') {
                        $(this).val('');
                        $('#search-clear').hide();
                        showAllServices();
                    }
                });

                // Handle clear icon click
                $('#search-clear').on('click', function () {
                    $('#service-search').val('');
                    $(this).hide();
                    showAllServices();
                    $('#service-search').focus();
                });

                // Add click to clear search when clicking outside
                $(document).on('click', function (e) {
                    if (!$(e.target).closest('.search-bar').length) {
                        // Optional: You can uncomment the line below to clear search when clicking outside
                        // $('#service-search').val('');
                        // showAllServices();
                    }
                });

                // Helper function to get current active tab context
                function getCurrentActiveTab() {
                    var context = {
                        type: 'all', // 'all', 'category', 'subcategory'
                        categoryId: null,
                        subcategoryId: null
                    };

                    // Check if we're on "All Services" tab
                    if ($('#all-services-tab').hasClass('active')) {
                        context.type = 'all';
                        return context;
                    }

                    // Check if we're on a category tab
                    $('.category-tab-btn.active').each(function () {
                        context.type = 'category';
                        context.categoryId = $(this).data('category-id');

                        // Check if there's an active subcategory within this category
                        var activeSubcategoryTab = $('#category-' + context.categoryId +
                            ' .nav-pills .nav-link.active');
                        if (activeSubcategoryTab.length > 0) {
                            context.type = 'subcategory';
                            var subcategoryTabId = activeSubcategoryTab.attr('id');
                            context.subcategoryId = subcategoryTabId.replace('subcategory-', '').replace('-tab',
                                '');
                        }
                    });

                    return context;
                }

                // Helper functions for search
                function showAllServices() {
                    // Show all service rows
                    $('tbody tr.service-item').show();
                    // Show all subcategory tabs
                    $('[id^="subcategory-"][id$="-tab"]').show();
                    // Show all category tabs
                    $('[id^="category-"][id$="-tab"]').show();
                    // Hide no results message
                    hideNoResultsMessage();
                    // Clear any search highlights
                    clearHighlights();
                    // Hide search count
                    updateSearchCount(0, true);

                    // Reset to default tab state if needed
                    if ($('#all-services-tab').hasClass('active')) {
                        // Already on all services, just ensure visibility
                        $('#all-services').addClass('show active');
                    }
                }

                function hideAllServices() {
                    // Hide all service rows
                    $('tbody tr.service-item').hide();
                    // Don't hide subcategory tabs - let the search logic handle visibility
                }

                function showNoResultsMessage() {
                    hideNoResultsMessage(); // Remove existing message first

                    var noResultsHtml = `
                                    <div id="no-results-message" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">No services found</h5>
                                            <p class="text-muted">Try adjusting your search terms or <button type="button" class="btn btn-link p-0 text-decoration-underline" onclick="clearSearch()">clear search</button></p>
                                        </div>
                                    </div>
                                `;

                    // Add to the currently active tab
                    var $activeTab = $('.tab-pane.active .row').first();
                    if ($activeTab.length === 0) {
                        $activeTab = $('#all-services .row');
                    }
                    $activeTab.append(noResultsHtml);
                }

                function hideNoResultsMessage() {
                    $('#no-results-message').remove();
                }

                // Highlight search terms
                function highlightSearchTerm(text, searchTerm) {
                    if (!searchTerm || searchTerm.length < 2) return text;

                    var regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
                    return text.replace(regex, '<span class="search-highlight">$1</span>');
                }

                function clearHighlights() {
                    $('.service-name, .service-description, .service-category').each(function () {
                        var $this = $(this);
                        var text = $this.html();
                        if (text.includes('search-highlight')) {
                            $this.html(text.replace(/<span class="search-highlight">(.*?)<\/span>/gi, '$1'));
                        }
                    });
                }

                function updateSearchCount(count, hide = false) {
                    var $counter = $('#search-results-count');
                    if (hide || count === 0) {
                        $counter.hide();
                    } else {
                        var text = count === 1 ? '1 service found' : count + ' services found';
                        $counter.text(text).show();
                    }
                }

                // Global function to clear search
                window.clearSearch = function () {
                    $('#service-search').val('');
                    $('#search-clear').hide();
                    clearHighlights();
                    showAllServices();
                };

                // Function to update search context
                function updateSearchContext(type, categoryId, subcategoryId) {
                    // This function can be used to update any global context if needed
                    // For now, it's a placeholder for future enhancements
                }

            });
        </script>
        <script>
            $(document).ready(function () {
                function showToast(message, type = 'success') {
                    // Create toast element if it doesn't exist
                    if ($('#dynamic-toast').length === 0) {
                        $('body').append(
                            '<div id="dynamic-toast" style="position: fixed; bottom: 20px; right: 20px; color: white; padding: 8px 16px; border-radius: 4px; z-index: 9999; display: none; font-size: 14px; max-width: 250px; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>'
                        );
                    }

                    const $toast = $('#dynamic-toast');

                    // Set background color based on type
                    if (type === 'error' || type === 'danger') {
                        $toast.css('background-color', '#dc3545'); // Red for errors/remove
                    } else {
                        $toast.css('background-color', '#28a745'); // Green for success/add
                    }

                    $toast.text(message).fadeIn(300).delay(2000).fadeOut(400);
                }
                $('.main-heading-icon').click(function () {
                    var userId = $(this).data('user-id');
                    var heartIcon = $('#heart-icon');

                    $.ajax({
                        url: '{{ route('favorite_professionals') }}',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            professional: userId
                        },
                        success: function (response) {
                            if (response.status === 'success') {
                                if (response.action === 'added') {
                                    // Change to filled red heart
                                    heartIcon.removeClass('far').addClass('fas text-danger');
                                    showToast('Added to favourites', 'success');
                                } else if (response.action === 'removed') {
                                    // Change to outline heart
                                    heartIcon.removeClass('fas text-danger').addClass('far');
                                    showToast('Removed from favourites', 'danger');
                                }
                            }
                        },
                        error: function (xhr, status, error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                showConfirmButton: true
                            });
                        }
                    });
                });

                // Handle Generate/Copy Short URL button
                $('#generateShortUrlBtn').on('click', function () {
                    const userId = $(this).data('user-id');
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // Show loading state
                    // $btn.prop('disabled', true).html(
                    //     '<span class="spinner-border spinner-border-sm me-1"></span>Loading...');

                    $.ajax({
                        url: '{{ route('generate_short_url') }}',
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            user_id: userId
                        },
                        success: function (response) {
                            if (response.success) {
                                // Copy to clipboard
                                navigator.clipboard.writeText(response.short_url).then(function () {
                                    // Show success message
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success!',
                                        text: 'Short URL copied to clipboard!',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });

                                    // Update button text
                                    // $btn.html(
                                    //     '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                    // );
                                }).catch(function () {
                                    // Fallback: show URL in alert if clipboard fails
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Short URL Generated!',
                                        html: `Your short URL: <br><strong>${response.short_url}</strong><br><small>Please copy it manually</small>`,
                                        showConfirmButton: true
                                    });

                                    // Update button text
                                    $btn.html(
                                        '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                    );
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: response.message ||
                                        'Failed to generate short URL',
                                    showConfirmButton: true
                                });
                            }
                        },
                        error: function (xhr) {
                            let errorMessage = 'An error occurred';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: errorMessage,
                                showConfirmButton: true
                            });
                        },
                        complete: function () {
                            // Reset button state
                            $btn.prop('disabled', false);
                            if ($btn.html().includes('Loading')) {
                                $btn.html(originalText);
                            }
                        }
                    });
                });
            });
        </script>
    @endpush

    {{-- Professional Location Map - Standalone Implementation --}}
    @if ($user->profile && $user->profile->lat && $user->profile->lng)
        @push('js')
            <script
                src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.api_key') }}&libraries=places&v=weekly"
                async defer></script>
            <script>
                // Professional Location Map - Standalone Implementation
                function initProfessionalLocationMap() {
                    console.log('Initializing Professional Location Map...');

                    // Check if the professional location map element exists
                    const professionalMapElement = document.getElementById("professional-location-map");
                    if (!professionalMapElement) {
                        console.log('Professional location map element not found');
                        return;
                    }

                    // Check if Google Maps API is loaded
                    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                        console.warn('Google Maps API not loaded yet, retrying...');
                        setTimeout(initProfessionalLocationMap, 500);
                        return;
                    }

                    // Professional's coordinates from PHP
                    const professionalLocation = {
                        lat: {{ $user->profile->lat }},
                        lng: {{ $user->profile->lng }}
                                            };

                    console.log('Professional Location:', professionalLocation);

                    // Initialize the professional location map
                    const professionalMap = new google.maps.Map(professionalMapElement, {
                        center: professionalLocation,
                        zoom: 15,
                        mapTypeControl: false,
                        streetViewControl: false,
                        rotateControl: true,
                        scrollwheel: true,
                        disableDoubleClickZoom: false,
                        styles: [{
                            featureType: "poi",
                            elementType: "labels",
                            stylers: [{
                                visibility: "off"
                            }]
                        }]
                    });

                    // Add marker for professional's location
                    const professionalMarker = new google.maps.Marker({
                        position: professionalLocation,
                        map: professionalMap,
                        title: "{{ $user->name }}",
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <circle cx="20" cy="20" r="18" fill="#020C87" stroke="white" stroke-width="4"/>
                                                            <circle cx="20" cy="20" r="8" fill="white"/>
                                                        </svg>
                                                    `),
                            scaledSize: new google.maps.Size(40, 40),
                            anchor: new google.maps.Point(20, 20)
                        }
                    });

                    // Add info window
                    const professionalInfoWindow = new google.maps.InfoWindow({
                        content: `
                                                    <div style="padding: 10px; max-width: 250px;">
                                                        <h6 style="margin: 0 0 8px 0; color: #020C87; font-weight: 600;">{{ $user->name }}</h6>
                                                        <p style="margin: 0; color: #666; font-size: 14px;">{{ $user->profile->location ?? 'Professional Location' }}</p>
                                                        <div style="margin-top: 10px;">
                                                            <a href="https://www.google.com/maps/dir/?api=1&destination={{ $user->profile->lat }},{{ $user->profile->lng }}"
                                                               target="_blank"
                                                               style="color: #020C87; text-decoration: none; font-size: 13px;">
                                                                <i class="fas fa-directions"></i> Get Directions
                                                            </a>
                                                        </div>
                                                    </div>
                                                `
                    });

                    // Show info window on marker click
                    professionalMarker.addListener('click', function () {
                        professionalInfoWindow.open(professionalMap, professionalMarker);
                    });

                    console.log('Professional Location Map initialized successfully');
                }

                // Initialize when DOM is ready
                document.addEventListener('DOMContentLoaded', function () {
                    // Small delay to ensure all elements are rendered
                    setTimeout(initProfessionalLocationMap, 100);
                });

                // Also initialize when Google Maps API loads (if not already loaded)
                window.initMap = function () {
                    initProfessionalLocationMap();
                };
            </script>
        @endpush
    @endif