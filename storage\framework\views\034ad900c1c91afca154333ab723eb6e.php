<!-- Service Preferences Modal -->
<div class="modal fade card-details" id="service-preferences-modal" aria-labelledby="service-preferences-modal"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Service Preferences</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="service-preferences-form" action="<?php echo e(route('service-preferences.update')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <label for="categories" class="form-label form-input-labels">Select Categories</label>
                            <select class="form-select form-select-solid" data-control="select2"
                                data-placeholder="Select categories" multiple name="categories[]" id="categories">
                                <?php if(isset($categories)): ?>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"
                                            <?php echo e(in_array($category->id, auth()->user()->service_preferences()->pluck('category_id')->toArray()) ? 'selected' : ''); ?>>
                                            <?php echo e($category->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Update</button>
                </div>
            </form>
        </div>
    </div>
</div><?php /**PATH D:\git-file\anders\resources\views/dashboard/profile_settings/modal/service-preferences-modal.blade.php ENDPATH**/ ?>